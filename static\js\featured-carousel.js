// Featured Carousel Controller - Continuous Sliding
class FeaturedCarousel {
    constructor() {
        this.container = document.getElementById('featured-carousel-container');
        this.track = document.getElementById('carousel-track');
        this.recipes = [];
        this.init();
    }

    async init() {
        try {
            await this.loadRecipes();
            this.setupEventListeners();
        } catch (error) {
            console.error('Failed to initialize featured carousel:', error);
            this.showError();
        }
    }
    
    async loadRecipes() {
        try {
            // Load 15 random recipes from cached Django endpoint (24-hour cache)
            const response = await fetch('/api/featured-carousel/');
            const data = await response.json();

            if (data.success && data.recipes && data.recipes.length > 0) {
                // Convert Django API format to TheMealDB format for compatibility
                this.recipes = data.recipes.map(recipe => ({
                    idMeal: recipe.id,
                    strMeal: recipe.title,
                    strMealThumb: recipe.image_url || recipe.image,
                    strArea: recipe.area || 'International',
                    strCategory: recipe.category || 'Recipe'
                }));
                this.renderCarousel();
            } else {
                console.error('No recipes received from backend');
                this.showError();
            }
        } catch (error) {
            console.error('Error loading recipes from backend:', error);
            throw error;
        }
    }
    
    renderCarousel() {
        if (this.recipes.length === 0) return;

        // Create 5 slides with 3 recipes each
        const slides = [];
        for (let i = 0; i < 5; i++) {
            const slideRecipes = this.recipes.slice(i * 3, (i + 1) * 3);
            slides.push(slideRecipes);
        }

        this.track.innerHTML = slides.map(slideRecipes => `
            <div class="carousel-slide">
                ${slideRecipes.map(recipe => `
                    <div class="slide-item"
                         style="background-image: url('${recipe.strMealThumb}');">
                        <div class="slide-overlay" onclick="window.featuredCarousel.handleRecipeClick('${recipe.idMeal}')" style="cursor: pointer;">
                            <h3 class="slide-title">${recipe.strMeal}</h3>
                            <p class="slide-description">${recipe.strArea || 'International'} • ${recipe.strCategory || 'Recipe'}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
        `).join('');
    }
    
    setupEventListeners() {
        // Pause animation on hover
        this.container?.addEventListener('mouseenter', () => {
            this.track.style.animationPlayState = 'paused';
        });

        this.container?.addEventListener('mouseleave', () => {
            this.track.style.animationPlayState = 'running';
        });
    }
    

    
    handleRecipeClick(recipeId) {
        // Get recipe name from the clicked element
        const clickedElement = event?.target?.closest('.slide-overlay');
        const recipeName = clickedElement?.querySelector('.slide-title')?.textContent?.trim() || 'Unknown Recipe';

        // Add recipe view to search history
        if (window.searchHistoryManager && recipeName !== 'Unknown Recipe') {
            const source = window.location.pathname.includes('/discover') ? 'discovery' : 'home';
            window.searchHistoryManager.addRecipeViewEntry(recipeName, source);
        }

        // Navigate to the webapp's recipe detail page
        const recipeUrl = `/recipes/detail/themealdb_${recipeId}/`;
        window.location.href = recipeUrl;
    }

    getRecipeUrl(recipe) {
        // Return internal webapp URL for recipe details
        return `/recipes/detail/themealdb_${recipe.idMeal}/`;
    }

    showError() {
        this.track.innerHTML = `
            <div class="carousel-slide">
                <div class="slide-item loading-item">
                    <div class="slide-overlay">
                        <h3 class="slide-title">Unable to load recipes</h3>
                        <p class="slide-description">Please check your connection and try again</p>
                    </div>
                </div>
            </div>
        `;
    }
}

// Initialize carousel when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('featured-carousel-container')) {
        window.featuredCarousel = new FeaturedCarousel();
    }
});
