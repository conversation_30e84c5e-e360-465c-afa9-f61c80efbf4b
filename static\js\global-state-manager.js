/**
 * Global State Manager for Recipe Finder
 * Preserves user activity and state across page navigation
 * Only clears state when "New" button is clicked
 */

class GlobalStateManager {
    constructor() {
        this.stateKey = 'recipe_finder_global_state';
        this.sessionKey = 'recipe_finder_session_id';
        this.currentSessionId = this.getOrCreateSessionId();
        this.state = this.loadState();
        this.init();
    }

    init() {
        console.log('🌐 Global State Manager initialized');
        this.bindEvents();
        this.setupNavigationInterception();
    }

    /**
     * Generate or retrieve session ID for state management
     */
    getOrCreateSessionId() {
        let sessionId = sessionStorage.getItem(this.sessionKey);
        if (!sessionId) {
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            sessionStorage.setItem(this.sessionKey, sessionId);
        }
        return sessionId;
    }

    /**
     * Load state from localStorage
     */
    loadState() {
        try {
            const savedState = localStorage.getItem(this.stateKey);
            if (savedState) {
                const parsed = JSON.parse(savedState);
                // Validate session - if different session, start fresh
                if (parsed.sessionId !== this.currentSessionId) {
                    return this.getDefaultState();
                }
                return { ...this.getDefaultState(), ...parsed };
            }
        } catch (error) {
            console.error('Failed to load global state:', error);
        }
        return this.getDefaultState();
    }

    /**
     * Get default state structure
     */
    getDefaultState() {
        return {
            sessionId: this.currentSessionId,
            currentPage: 'home',
            lastUpdated: Date.now(),
            
            // Search page state
            search: {
                query: '',
                searchType: 'ingredients',
                cuisine: '',
                restrictIngredients: false,
                quickFilter: '',
                results: null,
                hasResults: false,
                searchTriggered: false
            },
            
            // Discovery page state
            discovery: {
                activeCategory: 'all',
                featuredRecipe: null,
                categoryRecipes: {},
                viewedRecipes: [],
                lastRandomizedTime: null
            },
            
            // UI state
            ui: {
                mobileSearchExpanded: false,
                advancedFiltersExpanded: false,
                scrollPosition: 0,
                lastNavigationSource: null
            },
            
            // User activity tracking
            activity: {
                searchCount: 0,
                recipesViewed: [],
                lastActiveTime: Date.now(),
                navigationHistory: []
            }
        };
    }

    /**
     * Save current state to localStorage
     */
    saveState() {
        try {
            this.state.lastUpdated = Date.now();
            this.state.activity.lastActiveTime = Date.now();
            localStorage.setItem(this.stateKey, JSON.stringify(this.state));
            console.log('💾 Global state saved:', this.state);
        } catch (error) {
            console.error('Failed to save global state:', error);
        }
    }

    /**
     * Update search state
     */
    updateSearchState(searchData) {
        this.state.search = { ...this.state.search, ...searchData };
        this.state.currentPage = 'search';
        this.saveState();
    }

    /**
     * Update discovery state
     */
    updateDiscoveryState(discoveryData) {
        this.state.discovery = { ...this.state.discovery, ...discoveryData };
        this.state.currentPage = 'discover';
        this.saveState();
    }

    /**
     * Update UI state
     */
    updateUIState(uiData) {
        this.state.ui = { ...this.state.ui, ...uiData };
        this.saveState();
    }

    /**
     * Track page navigation
     */
    trackNavigation(fromPage, toPage, preserveState = true) {
        this.state.activity.navigationHistory.push({
            from: fromPage,
            to: toPage,
            timestamp: Date.now(),
            preserveState: preserveState
        });
        
        // Keep only last 20 navigation entries
        if (this.state.activity.navigationHistory.length > 20) {
            this.state.activity.navigationHistory = this.state.activity.navigationHistory.slice(-20);
        }
        
        this.state.currentPage = toPage;
        this.saveState();
    }

    /**
     * Clear all state (called by "New" button only)
     */
    clearState() {
        console.log('🗑️ Clearing global state (New button clicked)');
        this.state = this.getDefaultState();
        this.saveState();
        
        // Also clear session to ensure fresh start
        sessionStorage.removeItem(this.sessionKey);
        this.currentSessionId = this.getOrCreateSessionId();
        this.state.sessionId = this.currentSessionId;
        this.saveState();
    }

    /**
     * Get current state for a specific page
     */
    getPageState(page) {
        switch (page) {
            case 'search':
                return this.state.search;
            case 'discover':
                return this.state.discovery;
            default:
                return null;
        }
    }

    /**
     * Check if state should be restored for current page
     */
    shouldRestoreState() {
        const currentPath = window.location.pathname;
        const urlParams = new URLSearchParams(window.location.search);
        
        // Don't restore if this is a fresh "New" navigation
        if (this.state.ui.lastNavigationSource === 'new') {
            this.state.ui.lastNavigationSource = null;
            this.saveState();
            return false;
        }
        
        // Don't restore if URL has search parameters (direct link or refresh)
        if (urlParams.has('search_query') || urlParams.has('search_type')) {
            return false;
        }
        
        return true;
    }

    /**
     * Restore state for current page
     */
    restorePageState() {
        if (!this.shouldRestoreState()) {
            return false;
        }

        const currentPath = window.location.pathname;
        
        if (currentPath === '/' || currentPath.includes('/search')) {
            return this.restoreSearchState();
        } else if (currentPath.includes('/discover')) {
            return this.restoreDiscoveryState();
        }
        
        return false;
    }

    /**
     * Restore search page state
     */
    restoreSearchState() {
        const searchState = this.state.search;
        
        if (!searchState.hasResults) {
            return false;
        }

        console.log('🔄 Restoring search state:', searchState);
        
        // Restore search form values
        const searchForm = document.querySelector('#main-search-box form');
        if (searchForm) {
            const queryInput = searchForm.querySelector('input[name="search_query"]');
            const typeInput = searchForm.querySelector('input[name="search_type"]');
            const cuisineInput = searchForm.querySelector('input[name="cuisine"]');
            
            if (queryInput) queryInput.value = searchState.query;
            if (typeInput) typeInput.value = searchState.searchType;
            if (cuisineInput) cuisineInput.value = searchState.cuisine;
            
            // Update Alpine.js data if available
            if (searchForm._x_dataStack && searchForm._x_dataStack[0]) {
                const alpineData = searchForm._x_dataStack[0];
                alpineData.filter = searchState.searchType;
                alpineData.hasExistingResults = searchState.hasResults;
            }
        }
        
        // Trigger search restoration if we have results
        if (searchState.results) {
            setTimeout(() => {
                this.triggerSearchRestoration(searchState);
            }, 100);
        }
        
        return true;
    }

    /**
     * Restore discovery page state
     */
    restoreDiscoveryState() {
        const discoveryState = this.state.discovery;
        
        console.log('🔄 Restoring discovery state:', discoveryState);
        
        // Restore active category
        setTimeout(() => {
            const categoryChips = document.querySelectorAll('.category-chip');
            categoryChips.forEach(chip => {
                chip.classList.remove('active');
                if (chip.textContent.trim().toLowerCase() === discoveryState.activeCategory.toLowerCase()) {
                    chip.classList.add('active');
                }
            });
            
            // Update Alpine.js data if available
            const discoverContainer = document.querySelector('[x-data*="discoverPage"]');
            if (discoverContainer && discoverContainer._x_dataStack && discoverContainer._x_dataStack[0]) {
                discoverContainer._x_dataStack[0].activeCategory = discoveryState.activeCategory;
            }
        }, 100);
        
        return true;
    }

    /**
     * Trigger search restoration with preserved results
     */
    triggerSearchRestoration(searchState) {
        // This would ideally restore the actual search results
        // For now, we'll trigger a new search with the same parameters
        const searchForm = document.querySelector('#main-search-box form');
        if (searchForm && window.htmx) {
            console.log('🔄 Triggering search restoration');
            window.htmx.trigger(searchForm, 'submit');
        }
    }

    /**
     * Setup navigation interception
     */
    setupNavigationInterception() {
        // Intercept all navigation links
        document.addEventListener('click', (event) => {
            const link = event.target.closest('a[href]');
            if (link && this.shouldInterceptNavigation(link)) {
                this.handleNavigationClick(event, link);
            }
        });
        
        // Handle browser back/forward
        window.addEventListener('popstate', () => {
            setTimeout(() => {
                this.restorePageState();
            }, 100);
        });
    }

    /**
     * Check if navigation should be intercepted
     */
    shouldInterceptNavigation(link) {
        const href = link.getAttribute('href');
        
        // Don't intercept external links
        if (href.startsWith('http') || href.startsWith('//')) {
            return false;
        }
        
        // Don't intercept hash links
        if (href.startsWith('#')) {
            return false;
        }
        
        // Don't intercept if it's a "New" button
        if (link.classList.contains('sidebar-new-link') || 
            link.closest('[data-page="new"]') ||
            link.textContent.trim().toLowerCase().includes('new')) {
            return false;
        }
        
        return true;
    }

    /**
     * Handle navigation click with state preservation
     */
    handleNavigationClick(event, link) {
        const href = link.getAttribute('href');
        const currentPage = this.getCurrentPageType();
        const targetPage = this.getPageTypeFromUrl(href);
        
        // Save current page state before navigation
        this.saveCurrentPageState();
        
        // Track navigation
        this.trackNavigation(currentPage, targetPage, true);
        
        console.log(`🧭 Navigating from ${currentPage} to ${targetPage} with state preservation`);
    }

    /**
     * Save current page state before navigation
     */
    saveCurrentPageState() {
        const currentPage = this.getCurrentPageType();
        
        if (currentPage === 'search') {
            this.captureSearchState();
        } else if (currentPage === 'discover') {
            this.captureDiscoveryState();
        }
        
        // Save scroll position
        this.state.ui.scrollPosition = window.scrollY;
        this.saveState();
    }

    /**
     * Capture current search state
     */
    captureSearchState() {
        const searchForm = document.querySelector('#main-search-box form');
        if (searchForm) {
            const formData = new FormData(searchForm);
            const hasResults = document.querySelector('#recipe-results')?.children.length > 0;
            
            this.updateSearchState({
                query: formData.get('search_query') || '',
                searchType: formData.get('search_type') || 'ingredients',
                cuisine: formData.get('cuisine') || '',
                restrictIngredients: formData.get('restrict_ingredients') === 'true',
                quickFilter: formData.get('quick_filter') || '',
                hasResults: hasResults,
                searchTriggered: hasResults
            });
        }
    }

    /**
     * Capture current discovery state
     */
    captureDiscoveryState() {
        const activeCategory = document.querySelector('.category-chip.active')?.textContent?.trim() || 'all';
        
        this.updateDiscoveryState({
            activeCategory: activeCategory.toLowerCase(),
            lastRandomizedTime: Date.now()
        });
    }

    /**
     * Get current page type from URL
     */
    getCurrentPageType() {
        return this.getPageTypeFromUrl(window.location.pathname);
    }

    /**
     * Get page type from URL path
     */
    getPageTypeFromUrl(path) {
        if (path.includes('/discover')) return 'discover';
        if (path.includes('/search') || path === '/') return 'search';
        if (path.includes('/history')) return 'history';
        return 'other';
    }

    /**
     * Bind global events
     */
    bindEvents() {
        // Save state periodically
        setInterval(() => {
            this.saveState();
        }, 30000); // Every 30 seconds
        
        // Save state before page unload
        window.addEventListener('beforeunload', () => {
            this.saveCurrentPageState();
        });
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.saveCurrentPageState();
            }
        });
    }
}

// Initialize global state manager
window.globalStateManager = new GlobalStateManager();

// Make it available globally
window.GlobalStateManager = GlobalStateManager;
