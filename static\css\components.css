/* ========================================
   RECIPE FINDER - COMPONENT STYLES
   Reusable UI components including buttons, forms, navigation, cards, and modals
   ======================================== */

/* ========================================
   BUTTONS
   ======================================== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: none;
  white-space: nowrap;
  min-height: 44px;
  min-width: 44px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--text-on-primary);
  border-color: var(--primary-color);
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--accent-color) 100%);
  border-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
  color: var(--text-on-secondary);
  border-color: var(--secondary-color);
}

.btn-secondary:hover,
.btn-secondary:focus,
.btn-secondary:active {
  background: linear-gradient(135deg, var(--secondary-dark) 0%, var(--accent-color) 100%);
  border-color: var(--secondary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);
}

.btn-outline {
  background: #ffffff;
  border: 2px solid #e5e7eb;
  color: #374151;
}

.btn-outline:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

/* ========================================
   FORM ELEMENTS
   ======================================== */
.form-control,
.search-input,
input[type="text"],
input[type="search"],
input[type="email"],
textarea,
select {
  width: 100%;
  padding: 12px 16px;
  font-size: 16px; /* Prevent zoom on iOS */
  border: 2px solid #e5e7eb;
  border-radius: var(--radius-lg);
  background-color: #ffffff;
  color: var(--text-dark);
  transition: all var(--transition-base);
  min-height: 48px;
}

.form-control:focus,
.search-input:focus,
input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-control::placeholder,
.search-input::placeholder,
input::placeholder,
textarea::placeholder {
  color: var(--text-muted);
  font-weight: 400;
}

/* Search Form Components */
.search-container {
  max-width: 800px;
  margin: 0 auto;
}

.search-form-wrapper {
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 4px;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-slow);
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-form-wrapper:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1), var(--shadow-md);
}

/* Search Home Icon */
.search-home-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  position: relative;
}

.search-home-icon:hover {
  background: #fff3f0;
  color: #FF6B35;
  transform: scale(1.05);
}

.search-home-icon svg {
  transition: all 0.2s ease;
}

.search-type-select {
  background: #f9fafb;
  border: none;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all var(--transition-base);
  min-width: 120px;
  flex: 0 0 auto;
}

/* Desktop-specific search input sizing */
@media (min-width: 993px) {
  .search-form-wrapper .search-type-select {
    flex: 0 0 78px; /* Reduced by 35% from ~120px */
    min-width: 78px;
  }

  .search-form-wrapper .search-input {
    flex: 1 1 auto; /* Increased to take up more space (35% more relative space) */
    min-width: 0; /* Allow shrinking if needed */
  }
}

.search-type-select:focus {
  outline: none;
  background: #f3f4f6;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.search-btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 20px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-base);
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 100px;
  justify-content: center;
}

.search-btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--accent-color) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.search-btn-primary:active {
  transform: translateY(0);
}

/* ========================================
   FEATURED CAROUSEL COMPONENT - CONTINUOUS SLIDING
   ======================================== */
.featured-carousel-container {
  max-width: 1200px;
  width: 100%;
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
  margin: var(--space-4) auto;
}

.progress-bar {
  height: 3px;
  background: rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  width: 100%;
  animation: progressMove 60s linear infinite;
}

@keyframes progressMove {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(0); }
}

.carousel-wrapper {
  position: relative;
  height: 220px;
  overflow: hidden;
}

.carousel-track {
  display: flex;
  animation: continuousSlide 60s linear infinite;
  height: 100%;
}

@keyframes continuousSlide {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-500%);
  }
}

.carousel-track:hover {
  animation-play-state: paused;
}

.carousel-slide {
  min-width: 100%;
  display: flex;
  gap: 15px;
  padding: 20px;
  align-items: center;
}

.slide-item {
  flex: 1;
  height: 100%;
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.078);
  transition: transform 0.3s ease;
}

.slide-item:hover {
  transform: translateY(-5px);
}

.slide-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 25px;
  color: white;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.slide-item:hover .slide-overlay {
  transform: translateY(0);
}

.slide-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: white; /* Same as slide description */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.slide-description {
  font-size: 0.85rem;
  opacity: 0.9;
  line-height: 1.4;
}

/* Loading states */
.loading-item {
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-item .slide-overlay {
  transform: translateY(0);
  background: rgba(0, 0, 0, 0.6);
  text-align: center;
}

/* Call to Action Overlay */
.carousel-cta-overlay {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.carousel-cta-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
  text-decoration: none;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-weight: 600;
  font-size: 0.875rem;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
}

.carousel-cta-button:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  text-decoration: none;
}

/* ========================================
   CARDS
   ======================================== */
.card {
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-slow);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.card-title {
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-2);
}

.card-footer {
  background-color: var(--neutral-color) !important;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: var(--space-4);
}

/* Recipe Cards */
.recipe-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-slow);
  cursor: pointer;
  text-decoration: none;
  color: inherit;
}

.recipe-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
  text-decoration: none;
  color: inherit;
}

.recipe-image {
  width: 100%;
  height: 180px;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  position: relative;
  overflow: hidden;
}

.recipe-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: saturate(1.1);
  transition: transform var(--transition-slow);
}

.recipe-card:hover .recipe-image img {
  transform: scale(1.05);
}

/* Recipe Content Styling */
.recipe-content {
  padding: var(--space-4);
}

.recipe-title {
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  line-height: 1.3;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: var(--space-3);
}

.recipe-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-1);
}

.recipe-tags .tag {
  font-size: 0.75rem;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  background-color: var(--surface);
  color: var(--text-secondary);
  border: 1px solid var(--border);
  transition: all var(--transition-base);
}

.recipe-tags .tag:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.recipe-tags .themealdb-tag {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Recipe Info Cards */
.recipe-info {
  padding: var(--space-4);
}

.recipe-area {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: 0.875rem;
}

/* ========================================
   NAVIGATION COMPONENTS
   ======================================== */

/* Sidebar Navigation */
.sidebar {
  width: 70px;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem 0;
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  z-index: 1000;
  border-right: 1px solid #f0f0f0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}

/* Search History Drawer */
.search-history-drawer {
    position: fixed;
    top: 0;
    left: 70px;
    width: 240px;
    height: 100%;
    background: #ffffff;
    overflow-y: auto;
    box-shadow: -4px 0 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    transform: translateX(-100%);
    z-index: 999;
    padding: var(--space-4);
}

/* Structured Date Groups */
.search-history-date-group h5 {
    margin: 0;
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    background: var(--primary-50);
    color: var(--text-dark);
}
.search-history-drawer ul {
    margin: 0;
    padding: 0;
    list-style: none;
}
.search-history-drawer li.search-history-item {
    padding: var(--space-2) var(--space-3);
}
.search-history-drawer.open {
    transform: translateX(0);
}
.search-history-drawer .search-history-item {
    cursor: pointer;
    border-bottom: 1px solid #e5e7eb;
}
.search-history-drawer .search-history-item:hover {
    background-color: #f9fafb;
}

/* Detailed mobile drawer item styles */
.search-history-drawer .search-history-item-main {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.search-history-drawer .search-history-item-query {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    line-height: 1.3;
}

.search-history-drawer .search-history-item-meta {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
    font-size: 11px;
    color: #666;
}

.search-history-drawer .search-history-item-type {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    text-transform: capitalize;
}

.search-history-drawer .search-history-item-type.recipe_view {
    background: #e8f5e8;
    color: #2e7d32;
}

.search-history-drawer .search-history-item-type.ingredients {
    background: #fff3e0;
    color: #f57c00;
}

.search-history-drawer .search-history-item-type.recipe_name {
    background: #fce4ec;
    color: #c2185b;
}

.search-history-drawer .search-history-item-time {
    color: #888;
    font-size: 10px;
}

.search-history-drawer .search-history-item-results {
    color: #666;
    font-size: 10px;
    font-weight: 500;
}

/* Desktop dropdown styles - match mobile history page styling */
.search-history-dropdown .history-item-main {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.search-history-dropdown .history-item-query {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    line-height: 1.3;
}

.search-history-dropdown .history-item-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    font-size: 11px;
    color: #6b7280;
}

.search-history-dropdown .history-item-type {
    background: #f3f4f6;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
    font-size: 10px;
    text-transform: capitalize;
}

.search-history-dropdown .history-item-type.ingredients {
    background: #dcfce7;
    color: #166534;
}

.search-history-dropdown .history-item-type.recipe_name {
    background: #dbeafe;
    color: #1e40af;
}

.search-history-dropdown .history-item-type.recipe_view {
    background: #e8f5e8;
    color: #2e7d32;
}

.search-history-dropdown .history-item-type.cuisine {
    background: #fef3c7;
    color: #92400e;
}

.search-history-dropdown .history-item-type.dietary {
    background: #f3e8ff;
    color: #7c3aed;
}

.search-history-dropdown .history-item-type.time {
    background: #fce7f3;
    color: #be185d;
}

.search-history-dropdown .history-item-time {
    color: #888;
    font-size: 10px;
}

.search-history-dropdown .history-item-results {
    color: #059669;
    font-size: 10px;
    font-weight: 500;
}

/* Hide drawer on tablet and mobile */
@media (max-width: 992px) {
  .search-history-drawer {
    display: none !important;
  }
}

.sidebar-logo {
  margin-bottom: 2rem;
  padding: 0;
}

.sidebar-logo-img {
  width: 40px;
  height: auto;
  max-width: 100%;
}

.sidebar-new-button {
  margin-bottom: 2rem;
  padding: 0;
}

.sidebar-new-link,
.sidebar-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 0;
  color: #9ca3af;
  text-decoration: none;
  transition: all var(--transition-base);
  border-radius: 8px;
  width: 50px;
  margin: 0 auto;
  position: relative;
}

.sidebar-new-link:hover {
  color: #6b7280;
  background-color: #f9fafb;
}

.sidebar-link:hover,
.sidebar-link.active {
  color: #10b981;
  background-color: #ecfdf5;
}

.sidebar-link.active::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background-color: #10b981;
  border-radius: 2px;
}

.sidebar-link i,
.sidebar-new-link i {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
}

.sidebar-link span,
.sidebar-new-link span {
  font-size: 0.625rem;
  text-align: center;
  line-height: 1;
  font-weight: 500;
  letter-spacing: 0.025em;
}

.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.sidebar-item {
  width: 100%;
  display: flex;
  justify-content: center;
}

/* Hide sidebar on mobile */
@media (max-width: 768px) {
  .sidebar {
    display: none;
  }
}

/* Mobile Bottom Navigation - Enhanced with mobile-inspiration.html styling */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  z-index: 1000;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.4s ease;
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
  display: none; /* Hidden by default */
}

.mobile-bottom-nav.hidden {
  transform: translateY(100%);
  opacity: 0;
}

.mobile-nav-container {
  position: relative;
  width: 100%;
  max-width: 380px;
  height: 60px;
  background: var(--neutral-color);
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 0 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.mobile-nav-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px 12px;
  border-radius: 20px;
  z-index: 2;
}

.mobile-nav-item.active {
  background: rgba(76, 175, 80, 0.1);
}

.mobile-nav-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
  stroke: var(--text-light);
  fill: none;
  stroke-width: 2;
  /* For Bootstrap icons */
  color: var(--text-light);
  font-size: 24px;
}

.mobile-nav-item.active .mobile-nav-icon {
  stroke: var(--secondary-color);
  /* For Bootstrap icons */
  color: var(--secondary-color);
}

/* Custom SVG icons for mobile navigation */
.mobile-nav-icon.bookmark-icon,
.mobile-nav-icon.heart-icon,
.mobile-nav-icon.history-icon {
  width: 24px;
  height: 24px;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* Mobile User Icon and Popover */
.mobile-nav-user {
  position: relative;
}

.mobile-nav-icon.user-icon {
  width: 24px;
  height: 24px;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.mobile-nav-item.active .mobile-nav-icon.user-icon {
  stroke: #FF6B35;
}

.mobile-user-popover {
  position: absolute;
  bottom: calc(100% + 10px);
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  width: 320px;
  max-width: calc(100vw - 40px);
  overflow: hidden;
}

/* Popover Animations */
.popover-enter {
  transition: all 0.3s ease;
}

.popover-enter-start {
  opacity: 0;
  transform: translateX(-50%) translateY(10px) scale(0.95);
}

.popover-enter-end {
  opacity: 1;
  transform: translateX(-50%) translateY(0) scale(1);
}

.popover-leave {
  transition: all 0.2s ease;
}

.popover-leave-start {
  opacity: 1;
  transform: translateX(-50%) translateY(0) scale(1);
}

.popover-leave-end {
  opacity: 0;
  transform: translateX(-50%) translateY(10px) scale(0.95);
}

/* Mobile User Stats Section */
.mobile-user-stats-section {
  padding: 20px;
  border-bottom: 1px solid #f3f4f6;
  background: linear-gradient(135deg, #fff3f0, #ffffff);
}

.mobile-user-section-title {
  color: #FF6B35;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 15px 0;
}

.mobile-user-stats-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mobile-user-stat-card {
  background: white;
  border-radius: 12px;
  padding: 12px;
  border: 1px solid #ffe8e1;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 12px;
}

.mobile-user-stat-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.1);
}

.mobile-user-stat-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  background: #fff3f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-user-stat-content {
  flex: 1;
}

.mobile-user-stat-number {
  font-size: 1.2rem;
  font-weight: 700;
  color: #FF6B35;
  line-height: 1;
  margin-bottom: 2px;
}

.mobile-user-stat-label {
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Mobile User Actions Section */
.mobile-user-actions-section {
  padding: 20px;
  border-bottom: 1px solid #f3f4f6;
}

.mobile-user-actions-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-user-action-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 12px;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  min-height: 44px; /* Minimum touch target */
}

.mobile-user-action-item:hover {
  background: #fff3f0;
  border-color: #ffe8e1;
  color: inherit;
  text-decoration: none;
}

.mobile-user-action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: #f9fafb;
  border-radius: 8px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.mobile-user-action-item:hover .mobile-user-action-icon {
  background: #FF6B35;
  color: white;
}

.mobile-user-action-content {
  flex: 1;
}

.mobile-user-action-title {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  margin-bottom: 2px;
}

.mobile-user-action-subtitle {
  color: #6b7280;
  font-size: 12px;
}

/* Mobile User Close Section */
.mobile-user-close-section {
  padding: 15px 20px;
  background: #f9fafb;
}

.mobile-user-close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 10px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px; /* Minimum touch target */
}

.mobile-user-close-btn:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.mobile-nav-label {
  font-size: 12px;
  color: var(--text-light);
  font-weight: 500;
  transition: all 0.3s ease;
}

.mobile-nav-item.active .mobile-nav-label {
  color: var(--secondary-color);
}

/* New Button (Elevated) - Center button styling */
.mobile-nav-item.mobile-nav-new {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
  transition: all 0.3s ease;
}

.mobile-nav-item.mobile-nav-new:hover {
  transform: translateX(-50%) translateY(-2px);
  box-shadow: 0 12px 30px rgba(255, 107, 53, 0.5);
}

.mobile-nav-item.mobile-nav-new .mobile-nav-icon {
  color: var(--text-on-primary);
  stroke: var(--text-on-primary);
  margin-bottom: 0;
}

.mobile-nav-item.mobile-nav-new .mobile-nav-label {
  color: var(--text-on-primary);
  font-size: 10px;
  margin-top: 2px;
}

/* Curved cutout effect for elevated button */
.mobile-nav-container::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 40px;
  background: var(--neutral-color);
  border-radius: 0 0 40px 40px;
  z-index: 1;
}

.mobile-nav-container::after {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 50px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0 0 50px 50px;
  z-index: 0;
}

/* ========================================
   MOBILE RESPONSIVE ADJUSTMENTS
   ======================================== */
/* Featured Carousel Responsive */
@media (max-width: 768px) {
  .sidebar {
    display: none;
  }

  .search-form-wrapper {
    flex-direction: column;
    gap: var(--space-3);
    padding: var(--space-4);
  }

  .search-type-select,
  .search-input,
  .search-btn-primary {
    width: 100%;
    min-height: 48px;
  }

  /* Mobile carousel adjustments */
  .featured-carousel-container {
    margin: var(--space-4) auto;
  }

  .carousel-wrapper {
    height: 180px;
  }

  .carousel-slide {
    gap: 10px;
    padding: 15px;
  }

  .slide-item:nth-child(n+3) {
    display: none;
  }

  .slide-title {
    font-size: 0.99rem; /* 10% smaller: 1.1rem * 0.9 = 0.99rem */
  }

  .slide-description {
    font-size: 0.72rem; /* 10% smaller: 0.8rem * 0.9 = 0.72rem */
  }

  .slide-overlay {
    padding: 22.5px; /* 10% smaller: 25px * 0.9 = 22.5px */
    transform: translateY(0);
    background: rgba(0, 0, 0, 0.35); /* Changed opacity from 0.7 to 0.35 */
  }

  .carousel-cta-overlay {
    top: 15px;
    right: 15px;
  }

  .carousel-cta-button {
    padding: var(--space-1) var(--space-3);
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .carousel-wrapper {
    height: 150px;
  }

  .slide-title {
    font-size: 0.9rem; /* 10% smaller: 1rem * 0.9 = 0.9rem */
  }

  .slide-description {
    font-size: 0.675rem; /* 10% smaller: 0.75rem * 0.9 = 0.675rem */
  }

  .carousel-slide {
    padding: 9px; /* 10% smaller: 10px * 0.9 = 9px */
  }
}

@media (max-width: 768px) {
  .search-btn-primary {
    font-size: var(--font-size-base);
    padding: var(--space-4) var(--space-5);
  }

  .btn {
    min-height: 44px;
    min-width: 44px;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .mobile-bottom-nav {
    display: flex;
  }
}

@media (min-width: 769px) {
  .mobile-bottom-nav {
    display: none;
  }
}

/* Mobile Navigation Responsive Adjustments */
@media (max-width: 375px) {
  .mobile-nav-container {
    max-width: 350px;
  }

  .mobile-nav-item {
    padding: 6px 8px;
  }

  .mobile-nav-label {
    font-size: 9px;
  }
}

/* Smooth scroll behavior enhancement for mobile navigation */
html {
  scroll-behavior: smooth;
}

/* ========================================
   FILTER COMPONENTS
   ======================================== */

/* Quick Filters Container */
.quick-filters {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 24px 0;
  width: 100%;
}

.quick-filters .btn-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

/* Quick Filter Buttons */
.quick-filters .btn {
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 24px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  transition: all var(--transition-base);
  white-space: nowrap;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.quick-filters .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
  transition: left 0.5s ease;
}

.quick-filters .btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.quick-filters .btn:hover::before {
  left: 100%;
}

.quick-filters .btn.active {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-color: var(--primary-color);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.quick-filters .btn.active:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--accent-color) 100%);
  transform: translateY(-1px);
}



/* Ensure collapse is closed by default - more robust implementation */
.collapse:not(.show) {
  display: none !important;
}

.collapse.show {
  display: block !important;
}



/* Filter Sections */
.filter-section {
  background: #ffffff;
  border: 2px solid #f3f4f6;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: var(--shadow-sm);
}

.filter-section h6 {
  color: var(--text-dark);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f3f4f6;
}

/* Form Controls */
.form-check {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-check-input {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  transition: all var(--transition-base);
}

.form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.form-check-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
}

.btn-check:checked + .btn-outline-secondary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}









/* Mobile Filter Adjustments */
@media (max-width: 768px) {
  .quick-filters .btn {
    padding: 12px 24px; /* Larger mobile buttons */
    font-size: 16px; /* Prevent zoom on iOS */
    min-height: 44px;
  }

  .quick-filters {
    margin: 16px 0;
    padding: 0 var(--space-2);
  }
}

/* ========================================
   LOADING ANIMATIONS & HTMX INDICATORS
   ======================================== */

/* Basic HTMX indicator styles */
.htmx-indicator {
  display: none;
}

.htmx-request .htmx-indicator {
  display: inline-block;
}

.htmx-request.htmx-indicator {
  display: inline-block;
}

/* Search confirmation tooltip */
.search-confirmation-tooltip {
  animation: pulse 0.5s ease-in-out infinite alternate;
}

.search-confirmation-tooltip .tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #ffc107;
}

@keyframes pulse {
  from {
    transform: translateX(-50%) scale(1);
  }
  to {
    transform: translateX(-50%) scale(1.05);
  }
}

/* Food-Themed Loading Overlay */
.food-loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(2px);
  z-index: 2000;
  display: none;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-slow);
}

.food-loader-overlay.show {
  display: flex;
  opacity: 1;
}

/* Results area loading state */
.recipe-results-loading {
  position: relative;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(249, 250, 251, 0.8);
  border-radius: 12px;
  margin: 20px 0;
}

/* Main food loader container */
.food-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 40px;
  text-align: center;
}

/* Cooking pot animation */
.cooking-pot-loader {
  position: relative;
  width: 80px;
  height: 80px;
}

.pot {
  width: 60px;
  height: 40px;
  background: linear-gradient(145deg, var(--secondary-color), var(--secondary-dark));
  border-radius: 0 0 30px 30px;
  position: relative;
  margin: 20px auto 0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.pot::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 6px;
  background: #333;
  border-radius: 3px;
}

.steam {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  animation: steam 1.5s ease-in-out infinite;
}

.steam:nth-child(2) {
  left: 40%;
  animation-delay: 0.3s;
}

.steam:nth-child(3) {
  left: 60%;
  animation-delay: 0.6s;
}

@keyframes steam {
  0%, 100% {
    opacity: 0;
    transform: translateX(-50%) translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) translateY(-10px);
  }
}

/* Inline loading indicators for search buttons */
.search-loading-icon {
  display: none;
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.search-loading-icon.active {
  display: inline-block;
}

.search-loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Loading text animations */
.loading-text {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes fadeInOut {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}
