{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>{% block title %}African Recipe finder{% endblock %}</title>

    <!-- CSS Styles -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Consolidated Recipe Finder CSS -->
    <link rel="stylesheet" href="{% static 'css/base.css' %}">
    <link rel="stylesheet" href="{% static 'css/components.css' %}">
    <link rel="stylesheet" href="{% static 'css/pages.css' %}">

    {% block extra_css %}{% endblock %}

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- HTMX -->
    <script src="{% static 'js/htmx.min.js' %}"></script>

    <!-- Alpine.js -->
    <script src="{% static 'js/alpine.min.js' %}" defer></script>
</head>

<body class="home-page d-flex flex-column min-vh-100">
    <!-- Top Navigation Bar (shown after search) -->


    <!-- Mobile Hamburger Menu
    <div class="mobile-hamburger" onclick="toggleMobileNav()">
        <img src="{% static 'images/logos/hamburger-menu.webp' %}" alt="Menu" class="hamburger-icon">
    </div> -->

    <!-- Mobile Navigation Overlay -->
    <div class="mobile-nav-overlay" onclick="closeMobileNav()"></div>

  

    <div class="body-with-sidebar">
        <div class="sidebar">
            <!-- Logo at top of sidebar -->
            <div class="sidebar-logo">
                <img src="{% static 'images/logos/Recipe-Finder-logo.webp' %}" alt="Recipe Finder" class="sidebar-logo-img">
            </div>
            <!-- New button below logo -->
            <div class="sidebar-new-button">
                <a href="{% url 'recipes:home' %}" class="sidebar-new-link" title="Create New Recipe" onclick="handleNewButtonClick(event)">
                    <i class="bi bi-plus-circle"></i>
                    <span>New</span>
                </a>
            </div>
            <ul class="sidebar-nav">
                <li class="sidebar-item">
                    <a href="{% url 'recipes:home' %}" onclick="event.preventDefault(); handleNavigationWithStatePreservation('{% url 'recipes:home' %}');"
                        class="sidebar-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}">
                        <i class="bi bi-house"></i>
                        <span>Home</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="{% url 'recipes:discover' %}" onclick="event.preventDefault(); handleNavigationWithStatePreservation('{% url 'recipes:discover' %}');"
                        class="sidebar-link {% if request.resolver_match.url_name == 'discover' %}active{% endif %}">
                        <i class="bi bi-compass"></i>
                        <span>Discover</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" title="Bookmarks" onclick="showComingSoon('Bookmarks')">
                        <i class="bi bi-bookmark"></i>
                        <span>Bookmarks</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" title="Favourites" onclick="showComingSoon('Favourites')">
                        <i class="bi bi-heart"></i>
                        <span>Favourites</span>
                    </a>
                </li>

            </ul>
            
            <!-- Profile / User Drawer Component -->
            <div class="sidebar-profile" style="margin-top: auto; padding: 10px 0;">
                {% include 'recipes/components/_user_dropdown.html' %}
            </div>
        </div>
        <div class="d-flex flex-column flex-grow-1 w-100">
            <main class="main-content flex-grow-1">
                {% block content %}{% endblock %}
                <div class="top-nav-bar" id="top-nav-bar" style="display: none;">
                    <div class="top-nav-content">
                        <h1 class="top-nav-title">My Recipe Finder</h1>
                    </div>
                </div>
            </main>
            
            <footer class="bg-light mt-auto">
                <div class="container text-center">
                    <p>&copy; {% now "Y" %} Recipe finder. All rights reserved.</p>
                </div>
            </footer>
        </div>
    </div>

    <!-- Mobile Bottom Navigation (inspired by mobile-inspiration.html) -->
    <div class="mobile-bottom-nav" id="mobileBottomNav">
        <div class="mobile-nav-container">
            <div class="mobile-nav-item {% if request.resolver_match.url_name == 'home' %}active{% endif %}" data-page="home">
                <svg class="mobile-nav-icon home-icon" viewBox="0 0 24 24">
                    <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9,22 9,12 15,12 15,22"></polyline>
                </svg>
                <span class="mobile-nav-label">Home</span>
            </div>

            <div class="mobile-nav-item {% if request.resolver_match.url_name == 'search_history' %}active{% endif %}" data-page="history">
                <i class="bi bi-clock-history mobile-nav-icon"></i>
                <span class="mobile-nav-label">History</span>
            </div>

            <div class="mobile-nav-item mobile-nav-new" data-page="new">
                <svg class="mobile-nav-icon plus-icon" viewBox="0 0 24 24">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                <span class="mobile-nav-label">New</span>
            </div>



            <div class="mobile-nav-item {% if request.resolver_match.url_name == 'discover' %}active{% endif %}" data-page="discover">
                <i class="bi bi-compass mobile-nav-icon"></i>
                <span class="mobile-nav-label">Discover</span>
            </div>

            <!-- User Icon for Mobile -->
            <div class="mobile-nav-item mobile-nav-user" data-page="user" x-data="mobileUserPopover()" @click="togglePopover()">
                <svg class="mobile-nav-icon user-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                    <circle cx="12" cy="7" r="4"/>
                </svg>
                <span class="mobile-nav-label">Profile</span>

                <!-- Mobile User Popover -->
                <div class="mobile-user-popover" x-show="isOpen" x-transition:enter="popover-enter" x-transition:enter-start="popover-enter-start" x-transition:enter-end="popover-enter-end" x-transition:leave="popover-leave" x-transition:leave-start="popover-leave-start" x-transition:leave-end="popover-leave-end" @click.stop>

                    <!-- User Stats Section -->
                    <div class="mobile-user-stats-section">
                        <h4 class="mobile-user-section-title">Your Activity</h4>
                        <div class="mobile-user-stats-grid">
                            <div class="mobile-user-stat-card">
                                <div class="mobile-user-stat-icon">🔍</div>
                                <div class="mobile-user-stat-content">
                                    <div class="mobile-user-stat-number" id="mobile-user-total-searches">0</div>
                                    <div class="mobile-user-stat-label">Total Searches</div>
                                </div>
                            </div>
                            <div class="mobile-user-stat-card">
                                <div class="mobile-user-stat-icon">📅</div>
                                <div class="mobile-user-stat-content">
                                    <div class="mobile-user-stat-number" id="mobile-user-week-searches">0</div>
                                    <div class="mobile-user-stat-label">This Week</div>
                                </div>
                            </div>
                            <div class="mobile-user-stat-card">
                                <div class="mobile-user-stat-icon">⭐</div>
                                <div class="mobile-user-stat-content">
                                    <div class="mobile-user-stat-number" id="mobile-user-popular-type">-</div>
                                    <div class="mobile-user-stat-label">Most Used</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Actions Section -->
                    <div class="mobile-user-actions-section">
                        <h4 class="mobile-user-section-title">Quick Access</h4>
                        <div class="mobile-user-actions-grid">
                            <a href="{% url 'recipes:search_history' %}" class="mobile-user-action-item">
                                <div class="mobile-user-action-icon">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
                                        <path d="M3 3v5h5"/>
                                        <polyline points="12,7 12,12 16,16"/>
                                    </svg>
                                </div>
                                <div class="mobile-user-action-content">
                                    <div class="mobile-user-action-title">Search History</div>
                                    <div class="mobile-user-action-subtitle">View all searches</div>
                                </div>
                            </a>

                            <a href="#" class="mobile-user-action-item" onclick="showComingSoon('Favourites')">
                                <div class="mobile-user-action-icon">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                                    </svg>
                                </div>
                                <div class="mobile-user-action-content">
                                    <div class="mobile-user-action-title">Favourites</div>
                                    <div class="mobile-user-action-subtitle">Saved recipes</div>
                                </div>
                            </a>

                            <a href="#" class="mobile-user-action-item" onclick="showComingSoon('Bookmarks')">
                                <div class="mobile-user-action-icon">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"/>
                                    </svg>
                                </div>
                                <div class="mobile-user-action-content">
                                    <div class="mobile-user-action-title">Bookmarks</div>
                                    <div class="mobile-user-action-subtitle">Quick access</div>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- Close Button -->
                    <div class="mobile-user-close-section">
                        <button class="mobile-user-close-btn" @click="closePopover()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"/>
                                <line x1="6" y1="6" x2="18" y2="18"/>
                            </svg>
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="{% static 'js/main.js' %}"></script>

    <!-- Mobile Interactions -->
    <script src="{% static 'js/mobile-interactions.js' %}"></script>

    <!-- Mobile Navigation JavaScript -->
    <script>
        function toggleMobileNav() {
            const menu = document.getElementById('mobileNavMenu');
            const overlay = document.querySelector('.mobile-nav-overlay');

            if (menu.classList.contains('open')) {
                closeMobileNav();
            } else {
                menu.classList.add('open');
                overlay.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        }

        function closeMobileNav() {
            const menu = document.getElementById('mobileNavMenu');
            const overlay = document.querySelector('.mobile-nav-overlay');

            menu.classList.remove('open');
            overlay.style.display = 'none';
            document.body.style.overflow = '';
        }

        // Close menu when clicking on a nav link
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.mobile-nav-menu .nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', closeMobileNav);
            });
        });

        // Enhanced Mobile Bottom Navigation Scroll Behavior (inspired by mobile-inspiration.html)
        let lastScrollTop = 0;
        let scrollTimeout;
        const mobileBottomNav = document.getElementById('mobileBottomNav');
        const mobileHamburger = document.querySelector('.mobile-hamburger');
        const mobileNavItems = document.querySelectorAll('.mobile-nav-item');

        // Enhanced scroll behavior for aesthetic hiding/showing
        window.addEventListener('scroll', function() {
            // Only apply scroll behavior on mobile screens
            if (window.innerWidth > 768) return;

            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // Clear existing timeout
            clearTimeout(scrollTimeout);

            if (scrollTop > lastScrollTop && scrollTop > 60) {
                // Scrolling down - hide with smooth delay
                scrollTimeout = setTimeout(() => {
                    if (mobileBottomNav) mobileBottomNav.classList.add('hidden');
                    if (mobileHamburger) mobileHamburger.classList.add('hidden');
                }, 100);
            } else if (scrollTop < lastScrollTop) {
                // Scrolling up - show immediately
                if (mobileBottomNav) mobileBottomNav.classList.remove('hidden');
                if (mobileHamburger) mobileHamburger.classList.remove('hidden');
            }

            // Always show when at top
            if (scrollTop <= 0) {
                if (mobileBottomNav) mobileBottomNav.classList.remove('hidden');
                if (mobileHamburger) mobileHamburger.classList.remove('hidden');
            }

            lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
        });

        // Mobile Bottom Navigation item click handlers
        mobileNavItems.forEach(item => {
            item.addEventListener('click', function() {
                // Skip active class management for user item (handled by Alpine.js)
                if (!this.classList.contains('mobile-nav-user')) {
                    // Remove active class from all items except user
                    mobileNavItems.forEach(nav => {
                        if (!nav.classList.contains('mobile-nav-user')) {
                            nav.classList.remove('active');
                        }
                    });

                    // Add active class to clicked item (except for new button)
                    if (!this.classList.contains('mobile-nav-new')) {
                        this.classList.add('active');
                    }
                }

                // Handle different navigation actions
                const page = this.getAttribute('data-page');
                switch(page) {
                    case 'home':
                        handleNavigationWithStatePreservation("{% url 'recipes:home' %}");
                        break;
                    case 'history':
                        handleNavigationWithStatePreservation("{% url 'recipes:search_history' %}");
                        break;
                    case 'new':
                        // Enhanced bounce animation for new button
                        this.style.transform = 'translateX(-50%) translateY(-2px) scale(0.9)';
                        setTimeout(() => {
                            this.style.transform = 'translateX(-50%) translateY(-2px) scale(1)';
                        }, 200);

                        // Clear state and navigate to fresh home page after animation
                        setTimeout(() => {
                            handleNewButtonClick();
                        }, 300);
                        break;

                    case 'discover':
                        handleNavigationWithStatePreservation("{% url 'recipes:discover' %}");
                        break;
                    case 'user':
                        // User popover is handled by Alpine.js, no navigation needed
                        break;
                }
            });
        });

        // Enhanced touch feedback for mobile bottom navigation
        mobileNavItems.forEach(item => {
            item.addEventListener('touchstart', function(e) {
                // Only prevent default for user popover items to avoid conflicts
                if (!this.classList.contains('mobile-nav-user')) {
                    this.style.transform = this.classList.contains('mobile-nav-new') ?
                        'translateX(-50%) translateY(-2px) scale(0.9)' : 'scale(0.9)';
                }
            });

            item.addEventListener('touchend', function(e) {
                // Only prevent default for user popover items to avoid conflicts
                if (!this.classList.contains('mobile-nav-user')) {
                    setTimeout(() => {
                        this.style.transform = this.classList.contains('mobile-nav-new') ?
                            'translateX(-50%) translateY(-2px) scale(1)' : 'scale(1)';
                    }, 100);
                }
            });
        });

        // Prevent navigation hiding when interacting with nav items
        if (mobileBottomNav) {
            mobileBottomNav.addEventListener('touchstart', function(e) {
                e.stopPropagation();
            });

            mobileBottomNav.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }






        // Mobile User Popover Functions
        function mobileUserPopover() {
            return {
                isOpen: false,

                init() {
                    this.loadUserStats();
                },

                togglePopover() {
                    this.isOpen = !this.isOpen;
                    if (this.isOpen) {
                        this.loadUserStats();
                        // Add active class to the nav item
                        this.$el.classList.add('active');
                    } else {
                        this.$el.classList.remove('active');
                    }
                },

                closePopover() {
                    this.isOpen = false;
                    this.$el.classList.remove('active');
                },

                loadUserStats() {
                    if (window.searchHistoryManager) {
                        const stats = window.searchHistoryManager.getSearchStats();

                        document.getElementById('mobile-user-total-searches').textContent = stats.totalSearches;
                        document.getElementById('mobile-user-week-searches').textContent = stats.recentActivity.length;

                        // Find most popular search type
                        const searchTypes = stats.searchTypes;
                        const mostPopular = Object.keys(searchTypes).reduce((a, b) =>
                            searchTypes[a] > searchTypes[b] ? a : b, 'ingredients'
                        );
                        document.getElementById('mobile-user-popular-type').textContent =
                            stats.totalSearches > 0 ? mostPopular : '-';
                    }
                }
            }
        }

        // Global function for coming soon features
        function showComingSoon(feature) {
            alert(`${feature} feature coming soon! This will allow you to save and organize your favorite recipes.`);
        }

        // Handle "New" button click - clears all state
        function handleNewButtonClick(event) {
            if (event) {
                event.preventDefault();
            }

            console.log('🆕 New button clicked - clearing all state');

            // Clear global state
            if (window.globalStateManager) {
                window.globalStateManager.clearState();
                window.globalStateManager.state.ui.lastNavigationSource = 'new';
                window.globalStateManager.saveState();
            }

            // Navigate to fresh home page
            window.location.href = "{% url 'recipes:home' %}";
        }

        // Handle navigation with state preservation
        function handleNavigationWithStatePreservation(url) {
            console.log('🧭 Navigating with state preservation to:', url);

            // Save current state before navigation
            if (window.globalStateManager) {
                window.globalStateManager.saveCurrentPageState();
            }

            // Navigate normally - state will be restored on the target page
            window.location.href = url;
        }

        // Make functions globally available
        window.mobileUserPopover = mobileUserPopover;
        window.showComingSoon = showComingSoon;
        window.handleNewButtonClick = handleNewButtonClick;
        window.handleNavigationWithStatePreservation = handleNavigationWithStatePreservation;

    </script>
    <script src="{% static 'js/global-state-manager.js' %}" defer></script>
    <script src="{% static 'js/search-history.js' %}" defer></script>
    <script src="{% static 'js/search-history-drawer.js' %}" defer></script>
    <script src="{% static 'js/featured-carousel.js' %}" defer></script>

    {% block extra_js %}{% endblock %}
</body>

</html>