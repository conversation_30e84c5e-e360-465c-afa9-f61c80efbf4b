# State Preservation Test Plan

## Overview
This document outlines the test scenarios to verify that user activity and state are preserved during navigation in the Recipe Finder application.

## Test Scenarios

### 1. Search Page State Preservation

#### Test 1.1: Basic Search State Preservation
1. **Setup**: Navigate to home page (/)
2. **Action**: 
   - Enter search query "chicken"
   - Select search type "ingredients"
   - Perform search
   - Verify search results are displayed
3. **Navigation**: Click "Discover" in sidebar/mobile nav
4. **Expected**: Navigate to discovery page
5. **Return Navigation**: Click "Home" in sidebar/mobile nav
6. **Expected**: 
   - Search form should be populated with "chicken" and "ingredients"
   - Search results should be restored/re-triggered
   - UI should show search has been performed

#### Test 1.2: Advanced Search State Preservation
1. **Setup**: Navigate to home page (/)
2. **Action**:
   - Enter search query "pasta"
   - Select search type "name"
   - Add cuisine filter "Italian"
   - Apply quick filter if available
   - Perform search
3. **Navigation**: Navigate to discovery page
4. **Return Navigation**: Navigate back to home/search page
5. **Expected**: All search parameters and results should be restored

#### Test 1.3: Search Confirmation State
1. **Setup**: Perform a search with results
2. **Action**: Try to perform another search (should show confirmation)
3. **Navigation**: Navigate away and back
4. **Expected**: Confirmation state should be preserved

### 2. Discovery Page State Preservation

#### Test 2.1: Category Selection Preservation
1. **Setup**: Navigate to discovery page (/discover)
2. **Action**:
   - Click on a specific category (e.g., "Dessert")
   - Verify category becomes active and recipes load
3. **Navigation**: Navigate to home page
4. **Return Navigation**: Navigate back to discovery page
5. **Expected**: 
   - "Dessert" category should still be active
   - Category-specific recipes should be displayed

#### Test 2.2: Recipe Interaction Tracking
1. **Setup**: Navigate to discovery page
2. **Action**:
   - View/click on several recipe cards
   - Change categories
   - Randomize featured recipe
3. **Navigation**: Navigate away and back
4. **Expected**: 
   - Viewed recipes should be tracked in state
   - Active category should be preserved
   - User interactions should be maintained

### 3. Cross-Page Navigation State Preservation

#### Test 3.1: Multi-Page Navigation Flow
1. **Setup**: Start at home page
2. **Action Sequence**:
   - Perform search on home page
   - Navigate to discovery page
   - Select category on discovery page
   - Navigate back to home page
   - Navigate to discovery page again
   - Navigate to home page again
3. **Expected**: 
   - Each page should restore its previous state
   - Search results should be maintained on home page
   - Category selection should be maintained on discovery page

#### Test 3.2: Browser Back/Forward Navigation
1. **Setup**: Perform actions on multiple pages
2. **Action**: Use browser back/forward buttons
3. **Expected**: State should be preserved with browser navigation

### 4. "New" Button State Clearing

#### Test 4.1: Desktop Sidebar "New" Button
1. **Setup**: 
   - Perform searches and navigate between pages to build up state
   - Verify state is preserved during normal navigation
2. **Action**: Click "New" button in desktop sidebar
3. **Expected**:
   - All state should be cleared
   - Navigate to fresh home page
   - No search results or previous activity should be visible

#### Test 4.2: Mobile "New" Button
1. **Setup**: Same as 4.1 but on mobile device/viewport
2. **Action**: Click "New" button in mobile bottom navigation
3. **Expected**: Same as 4.1

#### Test 4.3: State Clearing Verification
1. **Setup**: After clicking "New" button
2. **Action**: Navigate between pages
3. **Expected**: 
   - No previous state should be restored
   - All pages should start fresh
   - New interactions should begin building new state

### 5. Session Management

#### Test 5.1: Page Refresh Behavior
1. **Setup**: Build up state through navigation and searches
2. **Action**: Refresh the page (F5 or Ctrl+R)
3. **Expected**: 
   - State should be cleared (fresh start)
   - URL parameters should take precedence if present

#### Test 5.2: New Tab/Window Behavior
1. **Setup**: Build up state in one tab
2. **Action**: Open application in new tab/window
3. **Expected**: New tab should start fresh (different session)

#### Test 5.3: Session Persistence
1. **Setup**: Build up state
2. **Action**: Close and reopen browser (same session storage)
3. **Expected**: State should be cleared (new session)

### 6. Mobile-Specific Tests

#### Test 6.1: Mobile Navigation State Preservation
1. **Setup**: Use mobile viewport (< 768px)
2. **Action**: Test all navigation scenarios using mobile bottom navigation
3. **Expected**: Same behavior as desktop but using mobile UI

#### Test 6.2: Mobile Search Expansion State
1. **Setup**: Mobile viewport
2. **Action**: 
   - Expand mobile search
   - Perform search
   - Navigate away and back
3. **Expected**: Mobile search state should be preserved

### 7. Error Handling and Edge Cases

#### Test 7.1: Invalid State Recovery
1. **Setup**: Manually corrupt localStorage state
2. **Action**: Navigate between pages
3. **Expected**: Application should gracefully handle invalid state

#### Test 7.2: Missing State Elements
1. **Setup**: Partially clear state data
2. **Action**: Navigate and interact
3. **Expected**: Application should handle missing state gracefully

#### Test 7.3: Large State Data
1. **Setup**: Build up large amount of state data
2. **Action**: Continue navigation and interactions
3. **Expected**: Performance should remain acceptable

## Success Criteria

### Primary Success Criteria
- ✅ Search results and form state preserved during navigation
- ✅ Discovery page category selections preserved during navigation  
- ✅ Only "New" button clears all state
- ✅ Normal navigation preserves user activity and progress
- ✅ Cross-platform consistency (mobile and desktop)

### Secondary Success Criteria
- ✅ Browser back/forward navigation works correctly
- ✅ Session management prevents state leakage between sessions
- ✅ Performance remains acceptable with state management
- ✅ Error handling for corrupted or missing state
- ✅ UI feedback indicates when state is being restored

## Testing Tools and Methods

### Manual Testing
- Test on multiple browsers (Chrome, Firefox, Safari, Edge)
- Test on multiple devices/viewports
- Test with browser developer tools console open to monitor state logs

### Automated Testing Considerations
- Browser console should show state management logs
- LocalStorage inspection to verify state structure
- Network tab to verify HTMX requests are working correctly

## Notes
- All tests should be performed on both desktop and mobile viewports
- Console logs should be monitored for state management messages
- LocalStorage should be inspected to verify state persistence
- Any failures should be documented with steps to reproduce
